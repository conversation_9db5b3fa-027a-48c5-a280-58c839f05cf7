Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/home/<USER>/.espressif/python_env/idf5.5_py3.12_env/bin/python -DESP_PLATFORM=1 -DIDF_TARGET=esp32s3 -DCCACHE_ENABLE=0 /home/<USER>/Desktop/esp-idf-video-streaming-main
-- Found Git: /usr/bin/git (found version "2.43.0")
-- Minimal build - OFF
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Dependencies lock doesn't exist, solving dependencies.
....NOTICE: Updating lock file at /home/<USER>/Desktop/esp-idf-video-streaming-main/dependencies.lock
NOTICE: Processing 3 dependencies:
NOTICE: [1/3] espressif/mdns (1.8.2)
NOTICE: [2/3] espressif/usb_host_uvc (1.0.4)
NOTICE: [3/3] idf (5.5.0)
-- Project sdkconfig file /home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig
Loading defaults file /home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults...
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:18 CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:19 CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:20 CONFIG_ESP32_WIFI_STATIC_TX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_STATIC_TX_BUFFER_NUM 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:21 CONFIG_ESP32_WIFI_CACHE_TX_BUFFER_NUM was replaced with CONFIG_ESP_WIFI_CACHE_TX_BUFFER_NUM 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:22 CONFIG_ESP32_WIFI_RX_BA_WIN was replaced with CONFIG_ESP_WIFI_RX_BA_WIN 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:38 CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240 was replaced with CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults:39 CONFIG_ESP32S3_DEFAULT_CPU_FREQ_MHZ was replaced with CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ 
warning: unknown kconfig symbol 'ESP32S2_DEFAULT_CPU_FREQ_240' assigned to 'y' in /home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32S2_DEFAULT_CPU_FREQ_MHZ' assigned to '240' in /home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: /home/<USER>/.espressif/python_env/idf5.5_py3.12_env/bin/python (found version "3.12.3") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- USING O3
-- App "video-streaming" version: 1
-- Adding linker script /home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_master.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_50.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_smp.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_dtm.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_test.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_scan.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libc.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console conversions cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_bitscrambler esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_twai esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__mdns espressif__usb_host_uvc esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: /home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace /home/<USER>/esp/v5.5-rc1/esp-idf/components/app_update /home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader /home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support /home/<USER>/esp/v5.5-rc1/esp-idf/components/bt /home/<USER>/esp/v5.5-rc1/esp-idf/components/cmock /home/<USER>/esp/v5.5-rc1/esp-idf/components/console /home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions /home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx /home/<USER>/esp/v5.5-rc1/esp-idf/components/driver /home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_app_format /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_bootloader_format /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_common /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ana_cmpr /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_bitscrambler /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_dac /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_isp /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_jpeg /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ledc /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_parlio /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_pcnt /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ppa /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdio /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdm /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdmmc /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_tsens /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_ota /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_server /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif_stack /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_ringbuf /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_vfs_console /home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi /home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump /home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns /home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc /home/<USER>/esp/v5.5-rc1/esp-idf/components/esptool_py /home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs /home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos /home/<USER>/esp/v5.5-rc1/esp-idf/components/hal /home/<USER>/esp/v5.5-rc1/esp-idf/components/heap /home/<USER>/esp/v5.5-rc1/esp-idf/components/http_parser /home/<USER>/esp/v5.5-rc1/esp-idf/components/idf_test /home/<USER>/esp/v5.5-rc1/esp-idf/components/ieee802154 /home/<USER>/esp/v5.5-rc1/esp-idf/components/json /home/<USER>/esp/v5.5-rc1/esp-idf/components/log /home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip /home/<USER>/Desktop/esp-idf-video-streaming-main/main /home/<USER>/esp/v5.5-rc1/esp-idf/components/mbedtls /home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt /home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib /home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash /home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_sec_provider /home/<USER>/esp/v5.5-rc1/esp-idf/components/openthread /home/<USER>/esp/v5.5-rc1/esp-idf/components/partition_table /home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon /home/<USER>/esp/v5.5-rc1/esp-idf/components/protobuf-c /home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm /home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread /home/<USER>/esp/v5.5-rc1/esp-idf/components/rt /home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc /home/<USER>/esp/v5.5-rc1/esp-idf/components/soc /home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash /home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs /home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport /home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element /home/<USER>/esp/v5.5-rc1/esp-idf/components/ulp /home/<USER>/esp/v5.5-rc1/esp-idf/components/unity /home/<USER>/esp/v5.5-rc1/esp-idf/components/usb /home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs /home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling /home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning /home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant /home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa
-- Configuring done (10.0s)
-- Generating done (1.0s)
-- Build files have been written to: /home/<USER>/Desktop/esp-idf-video-streaming-main/build
